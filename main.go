package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	_ "github.com/jackc/pgx/v5/stdlib" // 底線 _ 代表我們只需要這個 package 的 side-effects (註冊 driver)
)

// TrainingLog struct 對應到我們的 DB table
// 這是我們自製 ORM 的第一步，把 DB row 轉成 Go 的物件
type TrainingLog struct {
	ID        int64        `json:"id"`
	LiftType  string       `json:"lift_type"`
	WeightKG  float64      `json:"weight_kg"`
	Reps      int          `json:"reps"`
	Sets      int          `json:"sets"`
	LogDate   time.Time    `json:"log_date"`
	DeletedAt sql.NullTime `json:"deleted_at"` // 用 sql.NullTime 來處理可以為 NULL 的時間欄位
}

func main() {
	// 從環境變數讀取資料庫連線資訊，這是好習慣，不要 hardcode 在程式碼裡！
	// 格式: "postgres://user:password@localhost:5432/database_name"
	dbURL := os.Getenv("DATABASE_URL")
	if dbURL == "" {
		// 如果沒設環境變數，就用一個預設值方便本機開發
		dbURL = "postgres://admin:password@localhost:5432/mydb?sslmode=disable"
		log.Println("WARNING: DATABASE_URL not set, using default value")
	}

	// --- 資料庫連線 ---
	db, err := sql.Open("pgx", dbURL)
	if err != nil {
		log.Fatalf("Unable to connect to database: %v\n", err)
	}
	// defer 執行完 main function 後再關閉連線
	defer db.Close()

	// 檢查一下是否真的連上了
	if err := db.Ping(); err != nil {
		log.Fatalf("Database ping failed: %v\n", err)
	}

	fmt.Println("Successfully connected to the database! 🐘")

	// --- Gin Router 設定 ---
	r := gin.Default()

	// 稍後我們的 CRUD routes 會加在這裡...
	// r.POST("/logs", createLogHandler)
	// r.GET("/logs", getLogsHandler)

	// r.GET("/logs/:id", getLogByIDHandler)
	// r.PUT("/logs/:id", updateLogHandler)
	// r.DELETE("/logs/:id", deleteLogHandler)

	// 啟動 server，預設跑在 8080 port
	fmt.Println("Starting server on :8080... 🚀")
	r.Run(":8080")
}
